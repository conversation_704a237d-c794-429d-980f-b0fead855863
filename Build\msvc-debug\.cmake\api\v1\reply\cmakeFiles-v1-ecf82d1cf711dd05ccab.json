{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/scripts/buildsystems/vcpkg.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "Build/msvc-debug/CMakeFiles/3.30.1/CMakeSystem.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/scripts/buildsystems/vcpkg.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeNinjaFindMake.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Platform/Windows-Determine-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/CrayClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/TIClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "Build/msvc-debug/CMakeFiles/3.30.1/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeRCCompiler.cmake.in"}, {"isGenerated": true, "path": "Build/msvc-debug/CMakeFiles/3.30.1/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeTestRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerSupport.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/MSVC-CXX-CXXImportStd.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "Build/msvc-debug/CMakeFiles/3.30.1/CMakeCXXCompiler.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/cli11/CLI11ConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/cli11/CLI11Config.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/cli11/CLI11Config-debug.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/cli11/CLI11Config-release.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/nlohmann_json/nlohmann_jsonConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/nlohmann_json/nlohmann_jsonConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/nlohmann_json/nlohmann_jsonTargets.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/fmt/fmt-config-version.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/fmt/fmt-config.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/fmt/fmt-targets.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/fmt/fmt-targets-debug.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/fmt/fmt-targets-release.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfigTargets.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfigTargets-debug.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfigTargets-release.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/GNUInstallDirs.cmake"}], "kind": "cmakeFiles", "paths": {"build": "C:/Code/scoop/Build/msvc-debug", "source": "C:/Code/scoop"}, "version": {"major": 1, "minor": 1}}